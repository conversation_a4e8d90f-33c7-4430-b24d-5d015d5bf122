from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from decimal import Decimal


class User(AbstractUser):
    """نموذج المستخدم المخصص"""
    USER_TYPES = (
        ('super_admin', 'مالك المشروع'),
        ('manager', 'مدير صندوق/جمعية'),
    )

    user_type = models.CharField(max_length=20, choices=USER_TYPES, default='manager')
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    is_active_user = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.username} - {self.get_user_type_display()}"


class Organization(models.Model):
    """نموذج المؤسسات/الصناديق/الجمعيات"""
    ORGANIZATION_TYPES = (
        ('regular', 'جمعية عادية'),
        ('cyclic', 'جمعية دورية'),
        ('family_fund', 'صندوق عائلة'),
    )

    name = models.CharField(max_length=200, verbose_name="اسم المؤسسة")
    description = models.TextField(blank=True, verbose_name="الوصف")
    address = models.TextField(blank=True, verbose_name="العنوان")
    phone = models.CharField(max_length=20, blank=True, verbose_name="الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    # حقول خاصة بالجمعية الدورية
    organization_type = models.CharField(
        max_length=20,
        choices=ORGANIZATION_TYPES,
        default='regular',
        verbose_name="نوع الجمعية"
    )
    monthly_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="المبلغ الشهري"
    )
    cycle_duration_days = models.IntegerField(
        default=30,
        verbose_name="مدة الدورة بالأيام"
    )
    cycle_start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ بداية الجمعية الدورية"
    )
    current_cycle = models.IntegerField(
        default=0,
        verbose_name="رقم الدورة الحالية"
    )
    is_cycle_active = models.BooleanField(
        default=False,
        verbose_name="هل الدورة نشطة"
    )

    # حقول خاصة بصندوق العائلة
    family_name = models.CharField(
        max_length=200,
        null=True,
        blank=True,
        verbose_name="اسم العائلة"
    )
    emergency_fund_target = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="هدف صندوق الطوارئ"
    )
    monthly_contribution = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="المساهمة الشهرية المطلوبة"
    )


    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "مؤسسة"
        verbose_name_plural = "المؤسسات"

    def __str__(self):
        return self.name

    def get_active_members_count(self):
        """الحصول على عدد الأعضاء النشطين"""
        return self.members.filter(is_active=True).count()

    def get_max_members_limit(self):
        """الحصول على الحد الأقصى لعدد الأعضاء"""
        try:
            return self.subscription.max_members
        except:
            return 50  # الحد الافتراضي

    def can_add_member(self):
        """التحقق من إمكانية إضافة عضو جديد"""
        return self.get_active_members_count() < self.get_max_members_limit()

    def get_remaining_member_slots(self):
        """الحصول على عدد الأماكن المتبقية للأعضاء"""
        return max(0, self.get_max_members_limit() - self.get_active_members_count())

    def get_total_members(self):
        return self.members.filter(is_active=True).count()

    def get_total_balance(self):
        total_payments = self.payments.aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0')

        total_expenses = self.expenses.aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0')

        return total_payments - total_expenses

    def is_cyclic_organization(self):
        """التحقق من كون الجمعية دورية"""
        return self.organization_type == 'cyclic'

    def is_family_fund(self):
        """التحقق من كون المؤسسة صندوق عائلة"""
        return self.organization_type == 'family_fund'

    def get_current_receiver(self):
        """الحصول على القابض الحالي في الجمعية الدورية"""
        if not self.is_cyclic_organization():
            return None
        return CyclicAssociationOrder.get_current_receiver(self)

    def get_next_receiver(self):
        """الحصول على القابض التالي"""
        if not self.is_cyclic_organization():
            return None
        next_position = (self.current_cycle or 0) + 1
        return self.cyclic_orders.filter(order_position=next_position).first()

    def can_modify_order(self):
        """التحقق من إمكانية تعديل الترتيب"""
        return not self.is_cycle_active and self.current_cycle <= 1

    def lock_receiving_order(self):
        """قفل ترتيب القبض"""
        self.cyclic_orders.update(is_order_locked=True)

    def generate_random_order(self):
        """إنشاء ترتيب عشوائي للأعضاء"""
        if not self.is_cyclic_organization():
            return False

        # حذف الترتيب الحالي إذا وجد
        self.cyclic_orders.all().delete()

        # الحصول على الأعضاء النشطين
        active_members = list(self.members.filter(is_active=True))

        if not active_members:
            return False

        # خلط الأعضاء عشوائياً
        import random
        random.shuffle(active_members)

        # إنشاء الترتيب
        for i, member in enumerate(active_members, 1):
            CyclicAssociationOrder.objects.create(
                organization=self,
                member=member,
                order_position=i
            )

        return True

    def is_regular_organization(self):
        """التحقق من كون المؤسسة جمعية عادية"""
        return self.organization_type == 'regular'

    def get_current_cycle_round(self):
        """الحصول على الدورة الحالية"""
        if not self.is_cyclic_organization():
            return None
        return self.cycle_rounds.filter(status='active').first()

    def get_next_cycle_number(self):
        """الحصول على رقم الدورة التالية"""
        if not self.is_cyclic_organization():
            return None
        last_round = self.cycle_rounds.order_by('-round_number').first()
        return (last_round.round_number + 1) if last_round else 1

    def can_start_new_cycle(self):
        """التحقق من إمكانية بدء دورة جديدة"""
        if not self.is_cyclic_organization():
            return False

        # التحقق من عدم وجود دورة نشطة
        active_round = self.get_current_cycle_round()
        if active_round:
            return False

        # التحقق من وجود أعضاء كافيين
        active_members = self.members.filter(is_active=True).count()
        return active_members >= 2

    def get_eligible_members_for_draw(self):
        """الحصول على الأعضاء المؤهلين للسحب في الدورة الحالية"""
        current_round = self.get_current_cycle_round()
        if not current_round:
            return Member.objects.none()

        # الأعضاء الذين دفعوا ولم يستلموا من قبل
        eligible_cycle_members = current_round.cycle_members.filter(
            payment_status='paid',
            is_eligible_for_draw=True,
            has_received_before=False
        )

        return Member.objects.filter(
            id__in=eligible_cycle_members.values_list('member_id', flat=True)
        )


class Member(models.Model):
    """نموذج الأعضاء"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='members')
    name = models.CharField(max_length=200, verbose_name="الاسم")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    email = models.EmailField(blank=True, null=True, verbose_name="البريد الإلكتروني")
    city = models.CharField(max_length=100, verbose_name="المدينة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "عضو"
        verbose_name_plural = "الأعضاء"
        unique_together = ['organization', 'phone']

    def __str__(self):
        return f"{self.name} - {self.organization.name}"

    def get_received_count(self):
        """الحصول على عدد مرات الاستلام في الجمعية الدورية"""
        if not self.organization.is_cyclic_organization():
            return 0
        return self.won_rounds.filter(organization=self.organization).count()

    def get_total_payments(self):
        return self.payments.aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0')

    def get_last_payment(self):
        return self.payments.order_by('-payment_date').first()

    def get_payment_status(self):
        """الحصول على حالة دفعات العضو"""
        from .utils import get_member_payment_status
        return get_member_payment_status(self)


class Payment(models.Model):
    """نموذج الدفعات"""
    PAYMENT_TYPES = (
        ('monthly', 'اشتراك شهري'),
        ('annual', 'اشتراك سنوي'),
        ('donation', 'تبرع'),
        ('other', 'أخرى'),
    )

    PAYMENT_METHODS = (
        ('cash', 'نقدي'),
        ('bank_transfer', 'تحويل بنكي'),
        ('online', 'دفع إلكتروني'),
        ('check', 'شيك'),
    )

    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='payments')
    member = models.ForeignKey(Member, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="المبلغ")
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPES, verbose_name="نوع الدفعة")
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS, verbose_name="طريقة الدفع")
    payment_date = models.DateField(verbose_name="تاريخ الدفع")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "دفعة"
        verbose_name_plural = "الدفعات"
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.member.name} - {self.amount} - {self.payment_date}"


class Expense(models.Model):
    """نموذج المصاريف"""
    EXPENSE_TYPES = (
        ('operational', 'مصاريف تشغيلية'),
        ('maintenance', 'صيانة'),
        ('utilities', 'خدمات'),
        ('salaries', 'رواتب'),
        ('other', 'أخرى'),
    )

    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='expenses')
    expense_type = models.CharField(max_length=20, choices=EXPENSE_TYPES, verbose_name="نوع المصروف")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="المبلغ")
    expense_date = models.DateField(verbose_name="تاريخ المصروف")
    beneficiary = models.CharField(max_length=200, verbose_name="المستفيد")
    description = models.TextField(verbose_name="الوصف")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "مصروف"
        verbose_name_plural = "المصاريف"
        ordering = ['-expense_date']

    def __str__(self):
        return f"{self.get_expense_type_display()} - {self.amount} - {self.expense_date}"


class Subscription(models.Model):
    """نموذج الاشتراكات"""
    SUBSCRIPTION_TYPES = (
        ('free', 'مجاني'),
        ('basic', 'أساسي'),
        ('premium', 'مميز'),
    )

    STATUS_CHOICES = (
        ('active', 'نشط'),
        ('expired', 'منتهي'),
        ('suspended', 'معلق'),
    )

    organization = models.OneToOneField(Organization, on_delete=models.CASCADE, related_name='subscription')
    subscription_type = models.CharField(max_length=20, choices=SUBSCRIPTION_TYPES, default='free')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    start_date = models.DateField(default=timezone.now)
    end_date = models.DateField(null=True, blank=True)
    max_members = models.IntegerField(default=50)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "اشتراك"
        verbose_name_plural = "الاشتراكات"

    def __str__(self):
        return f"{self.organization.name} - {self.get_subscription_type_display()}"

    def is_active(self):
        if self.status != 'active':
            return False
        if self.end_date and self.end_date < timezone.now().date():
            return False
        return True


class ActivityLog(models.Model):
    """نموذج سجل الأنشطة"""
    ACTION_TYPES = (
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, null=True, blank=True)
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    model_name = models.CharField(max_length=50, blank=True)
    object_id = models.CharField(max_length=50, blank=True)
    description = models.TextField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "سجل النشاط"
        verbose_name_plural = "سجلات الأنشطة"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.get_action_type_display()} - {self.created_at}"


class CycleRound(models.Model):
    """نموذج دورات الجمعية الدورية"""
    STATUS_CHOICES = (
        ('pending', 'في الانتظار'),
        ('active', 'نشطة'),
        ('completed', 'مكتملة'),
        ('cancelled', 'ملغية'),
    )

    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='cycle_rounds'
    )
    round_number = models.IntegerField(verbose_name="رقم الدورة")
    start_date = models.DateField(verbose_name="تاريخ بداية الدورة")
    end_date = models.DateField(verbose_name="تاريخ نهاية الدورة")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name="حالة الدورة"
    )
    winner_member = models.ForeignKey(
        'Member',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='won_rounds',
        verbose_name="العضو الفائز"
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0'),
        verbose_name="إجمالي المبلغ"
    )
    draw_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="تاريخ السحب"
    )
    is_amount_received = models.BooleanField(
        default=False,
        verbose_name="تم استلام المبلغ"
    )
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "دورة"
        verbose_name_plural = "الدورات"
        unique_together = ['organization', 'round_number']
        ordering = ['-round_number']

    def __str__(self):
        return f"{self.organization.name} - الدورة {self.round_number}"


class CycleMember(models.Model):
    """نموذج أعضاء الجمعية الدورية"""
    PAYMENT_STATUS_CHOICES = (
        ('paid', 'دفع'),
        ('not_paid', 'لم يدفع'),
        ('partial', 'دفع جزئي'),
    )

    MEMBER_STATUS_CHOICES = (
        ('active', 'نشط'),
        ('received', 'استلم'),
        ('excluded', 'مستبعد'),
    )

    cycle_round = models.ForeignKey(
        CycleRound,
        on_delete=models.CASCADE,
        related_name='cycle_members'
    )
    member = models.ForeignKey(
        Member,
        on_delete=models.CASCADE,
        related_name='cycle_participations'
    )
    payment_status = models.CharField(
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='not_paid',
        verbose_name="حالة الدفع"
    )
    member_status = models.CharField(
        max_length=20,
        choices=MEMBER_STATUS_CHOICES,
        default='active',
        verbose_name="حالة العضو"
    )
    paid_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0'),
        verbose_name="المبلغ المدفوع"
    )
    payment_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ الدفع"
    )
    is_eligible_for_draw = models.BooleanField(
        default=True,
        verbose_name="مؤهل للسحب"
    )
    has_received_before = models.BooleanField(
        default=False,
        verbose_name="استلم من قبل"
    )
    received_count = models.IntegerField(
        default=0,
        verbose_name="عدد مرات الاستلام"
    )
    # ترتيب القبض في الجمعية الدورية
    receiving_order = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="ترتيب القبض"
    )
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "عضو دورة"
        verbose_name_plural = "أعضاء الدورات"
        unique_together = ['cycle_round', 'member']

    def __str__(self):
        return f"{self.member.name} - الدورة {self.cycle_round.round_number}"


class CyclicAssociationOrder(models.Model):
    """نموذج ترتيب القبض في الجمعية الدورية"""
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='cyclic_orders'
    )
    member = models.ForeignKey(
        Member,
        on_delete=models.CASCADE,
        related_name='cyclic_orders'
    )
    order_position = models.IntegerField(verbose_name="ترتيب القبض")
    has_received = models.BooleanField(
        default=False,
        verbose_name="قبض"
    )
    received_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ القبض"
    )
    is_order_locked = models.BooleanField(
        default=False,
        verbose_name="الترتيب مقفل"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "ترتيب الجمعية الدورية"
        verbose_name_plural = "ترتيب الجمعيات الدورية"
        unique_together = ['organization', 'member']
        ordering = ['order_position']

    def __str__(self):
        return f"{self.member.name} - ترتيب {self.order_position} - {self.organization.name}"

    @classmethod
    def get_current_receiver(cls, organization):
        """الحصول على القابض الحالي"""
        current_cycle = organization.current_cycle
        if current_cycle and current_cycle > 0:
            return cls.objects.filter(
                organization=organization,
                order_position=current_cycle
            ).first()
        return None


