{% extends 'base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}لوحة تحكم صناديق العائلة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-piggy-bank me-2 text-primary"></i>
                    صناديق العائلة
                </h2>
                {% if user.user_type == 'super_admin' %}
                <a href="{% url 'family_fund_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء صندوق عائلة جديد
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    {% if organization and fund_stats %}
    <!-- إحصائيات الصندوق -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الرصيد الحالي</h6>
                            <h3 class="mb-0">{{ fund_stats.current_balance|format_currency }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wallet fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">هدف الطوارئ</h6>
                            <h3 class="mb-0">{{ fund_stats.emergency_fund_target|format_currency }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-target fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">عدد الأعضاء</h6>
                            <h3 class="mb-0">{{ organization.get_total_members }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المساهمة الشهرية</h6>
                            <h3 class="mb-0">{{ organization.monthly_contribution|format_currency }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تقدم هدف الطوارئ -->
    {% if fund_stats.emergency_fund_target %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        تقدم هدف صندوق الطوارئ
                    </h5>
                </div>
                <div class="card-body">
                    {% with fund_stats.current_balance as current %}
                    {% with fund_stats.emergency_fund_target as target %}
                    {% if target > 0 %}
                    {% widthratio current target 100 as progress_percent %}
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="progress" style="height: 25px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ progress_percent }}%">
                                    {{ progress_percent }}%
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <h5 class="mb-0">{{ current|format_currency }} / {{ target|format_currency }}</h5>
                        </div>
                    </div>
                    {% endif %}
                    {% endwith %}
                    {% endwith %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- إجراءات سريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <a href="{% url 'payment_create' %}" class="btn btn-outline-success w-100 mb-2">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                إضافة مساهمة
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'member_create' %}" class="btn btn-outline-warning w-100 mb-2">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة عضو
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'expense_create' %}" class="btn btn-outline-danger w-100 mb-2">
                                <i class="fas fa-receipt me-2"></i>
                                إضافة مصروف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- قائمة صناديق العائلة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        {% if user.user_type == 'super_admin' %}
                        جميع صناديق العائلة
                        {% else %}
                        صندوق عائلتي
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if organizations %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم الصندوق</th>
                                    <th>اسم العائلة</th>
                                    <th>عدد الأعضاء</th>
                                    <th>الرصيد الحالي</th>
                                    <th>هدف الطوارئ</th>
                                    <th>المساهمة الشهرية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for org in organizations %}
                                <tr>
                                    <td>
                                        <strong>{{ org.name }}</strong>
                                        {% if org.description %}
                                        <br><small class="text-muted">{{ org.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ org.family_name|default:"-" }}</td>
                                    <td>{{ org.get_total_members }}</td>
                                    <td>{{ org.get_total_balance|format_currency }}</td>
                                    <td>{{ org.emergency_fund_target|format_currency|default:"-" }}</td>
                                    <td>{{ org.monthly_contribution|format_currency|default:"-" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'member_list' %}?org={{ org.id }}"
                                               class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-users"></i>
                                                الأعضاء
                                            </a>
                                            <a href="{% url 'payment_list' %}?org={{ org.id }}"
                                               class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-money-bill-wave"></i>
                                                المدفوعات
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-piggy-bank fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد صناديق عائلة</h5>
                        {% if user.user_type == 'super_admin' %}
                        <p class="text-muted">ابدأ بإنشاء صندوق عائلة جديد</p>
                        <a href="{% url 'family_fund_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء صندوق عائلة
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
