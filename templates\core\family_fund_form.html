{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-piggy-bank me-2"></i>
                        {{ title }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" id="familyFundForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        <i class="fas fa-piggy-bank me-1"></i>
                                        اسم صندوق العائلة
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.family_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-users me-1"></i>
                                        اسم العائلة
                                    </label>
                                    {{ form.family_name }}
                                    {% if form.family_name.errors %}
                                        <div class="text-danger small">{{ form.family_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.emergency_fund_target.id_for_label }}" class="form-label">
                                        <i class="fas fa-target me-1"></i>
                                        هدف صندوق الطوارئ (شيقل)
                                    </label>
                                    {{ form.emergency_fund_target }}
                                    {% if form.emergency_fund_target.errors %}
                                        <div class="text-danger small">{{ form.emergency_fund_target.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">المبلغ المستهدف لصندوق الطوارئ</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.monthly_contribution.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-check me-1"></i>
                                        المساهمة الشهرية المطلوبة (شيقل)
                                    </label>
                                    {{ form.monthly_contribution }}
                                    {% if form.monthly_contribution.errors %}
                                        <div class="text-danger small">{{ form.monthly_contribution.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">المبلغ المطلوب من كل عضو شهرياً</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone me-1"></i>
                                        رقم الهاتف
                                    </label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>
                                        البريد الإلكتروني
                                    </label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>



                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-1"></i>
                                وصف الصندوق
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                            <ul class="mb-0">
                                <li>صندوق العائلة هو نظام لإدارة الأموال المشتركة بين أفراد العائلة</li>
                                <li>يمكن تحديد هدف لصندوق الطوارئ والعمل على تحقيقه</li>
                                <li>يتم تتبع المساهمات الشهرية والمصاريف</li>
                                <li>يمكن إضافة أفراد العائلة كأعضاء بعد إنشاء الصندوق</li>
                                <li>يمكن إنشاء تقارير مالية شاملة لمتابعة أداء الصندوق</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'organization_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                رجوع
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                إنشاء صندوق العائلة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}
