# Generated manually to remove loan-related models and fields

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_organization_allow_loans_and_more'),
    ]

    operations = [
        # Remove loan-related models
        migrations.DeleteModel(
            name='LoanRepayment',
        ),
        migrations.DeleteModel(
            name='FamilyLoan',
        ),
        
        # Remove loan-related fields from Organization
        migrations.RemoveField(
            model_name='organization',
            name='allow_loans',
        ),
        migrations.RemoveField(
            model_name='organization',
            name='max_loan_amount',
        ),

        # Add received_count field to CycleMember
        migrations.AddField(
            model_name='cyclemember',
            name='received_count',
            field=models.IntegerField(default=0, verbose_name='عدد مرات الاستلام'),
        ),

        # Add receiving_order field to CycleMember
        migrations.AddField(
            model_name='cyclemember',
            name='receiving_order',
            field=models.IntegerField(null=True, blank=True, verbose_name='ترتيب القبض'),
        ),

        # Create CyclicAssociationOrder model
        migrations.CreateModel(
            name='CyclicAssociationOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_position', models.IntegerField(verbose_name='ترتيب القبض')),
                ('has_received', models.BooleanField(default=False, verbose_name='قبض')),
                ('received_date', models.DateField(blank=True, null=True, verbose_name='تاريخ القبض')),
                ('is_order_locked', models.BooleanField(default=False, verbose_name='الترتيب مقفل')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('member', models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='cyclic_orders', to='core.member')),
                ('organization', models.ForeignKey(on_delete=models.deletion.CASCADE, related_name='cyclic_orders', to='core.organization')),
            ],
            options={
                'verbose_name': 'ترتيب الجمعية الدورية',
                'verbose_name_plural': 'ترتيب الجمعيات الدورية',
                'ordering': ['order_position'],
            },
        ),

        # Add unique constraint
        migrations.AddConstraint(
            model_name='cyclicassociationorder',
            constraint=models.UniqueConstraint(fields=['organization', 'member'], name='unique_org_member_order'),
        ),
    ]
