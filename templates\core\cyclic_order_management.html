{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}إدارة ترتيب القبض - {{ organization.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-sort-numeric-down me-2"></i>
                        إدارة ترتيب القبض
                    </h2>
                    <p class="text-muted mb-0">{{ organization.name }}</p>
                </div>
                <div>
                    <a href="{% url 'cyclic_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي الأعضاء</h6>
                                    <h3 class="mb-0">{{ total_members }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">أعضاء مرتبين</h6>
                                    <h3 class="mb-0">{{ ordered_members }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-sort-numeric-down fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">الدورة الحالية</h6>
                                    <h3 class="mb-0">{{ organization.current_cycle|default:"لم تبدأ" }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">المبلغ الشهري</h6>
                                    <h3 class="mb-0">{{ organization.monthly_amount|format_currency }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Receiver Alert -->
            {% if current_receiver %}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <h5 class="alert-heading">
                    <i class="fas fa-hand-holding-usd me-2"></i>
                    القابض الحالي
                </h5>
                <p class="mb-0">
                    <strong>{{ current_receiver.member.name }}</strong> - ترتيب {{ current_receiver.order_position }}
                    <br>
                    <small class="text-muted">هذا العضو يجب أن يقبض في الدورة الحالية</small>
                </p>
                {% if not current_receiver.has_received %}
                <hr>
                <form method="post" action="{% url 'mark_member_received' org_id=organization.id %}" class="d-inline">
                    {% csrf_token %}
                    <input type="hidden" name="member_id" value="{{ current_receiver.member.id }}">
                    <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('هل أنت متأكد من تسجيل قبض هذا العضو؟')">
                        <i class="fas fa-check me-1"></i>
                        تسجيل القبض
                    </button>
                </form>
                {% endif %}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إجراءات الترتيب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if can_modify_order %}
                        <div class="col-md-4">
                            <form method="post" action="{% url 'generate_random_order' org_id=organization.id %}">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-primary w-100 mb-2" onclick="return confirm('هل أنت متأكد من إنشاء ترتيب عشوائي جديد؟ سيتم حذف الترتيب الحالي.')">
                                    <i class="fas fa-random me-2"></i>
                                    إنشاء ترتيب عشوائي
                                </button>
                            </form>
                        </div>
                        
                        {% if ordered_members > 0 %}
                        <div class="col-md-4">
                            <form method="post" action="{% url 'lock_receiving_order' org_id=organization.id %}">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-success w-100 mb-2" onclick="return confirm('هل أنت متأكد من قفل الترتيب وبدء الدورة الأولى؟ لن يمكن تعديل الترتيب بعد ذلك.')">
                                    <i class="fas fa-lock me-2"></i>
                                    قفل الترتيب وبدء الدورة
                                </button>
                            </form>
                        </div>
                        {% endif %}
                        {% else %}
                        <div class="col-12">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                الترتيب مقفل ولا يمكن تعديله بعد بدء الدورة الأولى
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Members Order Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list-ol me-2"></i>
                        ترتيب الأعضاء
                    </h5>
                </div>
                <div class="card-body">
                    {% if orders %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الترتيب</th>
                                    <th>اسم العضو</th>
                                    <th>رقم الهاتف</th>
                                    <th>المدينة</th>
                                    <th>حالة القبض</th>
                                    <th>تاريخ القبض</th>
                                    {% if can_modify_order %}
                                    <th>الإجراءات</th>
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr {% if current_receiver and current_receiver.id == order.id %}class="table-warning"{% endif %}>
                                    <td>
                                        <span class="badge bg-primary fs-6">{{ order.order_position }}</span>
                                        {% if current_receiver and current_receiver.id == order.id %}
                                        <span class="badge bg-success ms-1">القابض الحالي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ order.member.name }}</strong>
                                    </td>
                                    <td>{{ order.member.phone|default:"-" }}</td>
                                    <td>{{ order.member.city|default:"-" }}</td>
                                    <td>
                                        {% if order.has_received %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>
                                            قبض
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-times me-1"></i>
                                            لم يقبض
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if order.received_date %}
                                        {{ order.received_date }}
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    {% if can_modify_order %}
                                    <td>
                                        <form method="post" action="{% url 'update_member_order' org_id=organization.id %}" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="member_id" value="{{ order.member.id }}">
                                            <div class="input-group input-group-sm" style="width: 120px;">
                                                <input type="number" name="new_position" class="form-control" 
                                                       value="{{ order.order_position }}" min="1" max="{{ ordered_members }}">
                                                <button type="submit" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-save"></i>
                                                </button>
                                            </div>
                                        </form>
                                    </td>
                                    {% endif %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-sort-numeric-down fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد ترتيب محدد بعد</h5>
                        <p class="text-muted">قم بإنشاء ترتيب عشوائي للأعضاء لبدء الجمعية الدورية</p>
                        {% if total_members > 0 and can_modify_order %}
                        <form method="post" action="{% url 'generate_random_order' org_id=organization.id %}">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-random me-2"></i>
                                إنشاء ترتيب عشوائي
                            </button>
                        </form>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
