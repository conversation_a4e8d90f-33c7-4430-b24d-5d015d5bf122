from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from functools import wraps
from django.db.models import Sum
from .models import ActivityLog
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from django.http import HttpResponse
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from datetime import datetime, timedelta
from django.utils import timezone
from decimal import Decimal
import random


def super_admin_required(view_func):
    """ديكوريتر للتأكد من أن المستخدم مالك المشروع"""
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if request.user.user_type != 'super_admin':
            messages.error(request, 'ليس لديك صلاحية للوصول لهذه الصفحة')
            return redirect('dashboard')
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def manager_required(view_func):
    """ديكوريتر للتأكد من أن المستخدم مدير صندوق"""
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if request.user.user_type not in ['manager', 'super_admin']:
            messages.error(request, 'ليس لديك صلاحية للوصول لهذه الصفحة')
            return redirect('dashboard')
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def organization_access_required(view_func):
    """ديكوريتر للتأكد من أن المستخدم يمكنه الوصول للمؤسسة"""
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if request.user.user_type == 'super_admin':
            return view_func(request, *args, **kwargs)
        
        # التحقق من أن المدير يصل لمؤسسته فقط
        org_id = kwargs.get('org_id') or request.GET.get('org_id')
        if org_id and str(request.user.organization.id) != str(org_id):
            messages.error(request, 'ليس لديك صلاحية للوصول لهذه المؤسسة')
            return redirect('dashboard')
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def log_activity(user, action_type, description, organization=None, model_name=None, object_id=None, ip_address=None):
    """تسجيل نشاط المستخدم"""
    try:
        ActivityLog.objects.create(
            user=user,
            organization=organization,
            action_type=action_type,
            model_name=model_name,
            object_id=str(object_id) if object_id else '',
            description=description,
            ip_address=ip_address
        )
    except Exception as e:
        print(f"خطأ في تسجيل النشاط: {e}")


def get_client_ip(request):
    """الحصول على عنوان IP للعميل"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def export_to_excel(data, filename, sheet_name="البيانات"):
    """تصدير البيانات إلى Excel"""
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{filename}.xlsx"'
    
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = sheet_name
    
    # تنسيق الرأس
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    if data:
        # كتابة الرؤوس
        headers = list(data[0].keys())
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # كتابة البيانات
        for row, item in enumerate(data, 2):
            for col, value in enumerate(item.values(), 1):
                worksheet.cell(row=row, column=col, value=value)
        
        # تعديل عرض الأعمدة
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    workbook.save(response)
    return response


def export_to_pdf(data, filename, title="تقرير"):
    """تصدير البيانات إلى PDF"""
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{filename}.pdf"'
    
    doc = SimpleDocTemplate(response, pagesize=A4, rightMargin=72, leftMargin=72,
                           topMargin=72, bottomMargin=18)
    
    # إعداد الأنماط
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1,  # وسط
    )
    
    story = []
    
    # العنوان
    story.append(Paragraph(title, title_style))
    story.append(Spacer(1, 12))
    
    if data:
        # إنشاء الجدول
        table_data = []
        headers = list(data[0].keys())
        table_data.append(headers)
        
        for item in data:
            table_data.append(list(item.values()))
        
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
    
    doc.build(story)
    return response


def get_monthly_stats(organization, year=None, month=None):
    """الحصول على إحصائيات شهرية"""
    if not year:
        year = timezone.now().year
    if not month:
        month = timezone.now().month
    
    # إحصائيات الدفعات
    payments = organization.payments.filter(
        payment_date__year=year,
        payment_date__month=month
    )
    
    # إحصائيات المصاريف
    expenses = organization.expenses.filter(
        expense_date__year=year,
        expense_date__month=month
    )
    
    total_payments = payments.aggregate(total=Sum('amount'))['total'] or Decimal('0')
    total_expenses = expenses.aggregate(total=Sum('amount'))['total'] or Decimal('0')
    
    return {
        'year': year,
        'month': month,
        'total_payments': total_payments,
        'total_expenses': total_expenses,
        'net_income': total_payments - total_expenses,
        'payments_count': payments.count(),
        'expenses_count': expenses.count(),
        'active_members': organization.get_total_members()
    }


def get_member_payment_status(member, months_back=3):
    """الحصول على حالة دفعات العضو"""
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=months_back * 30)
    
    payments = member.payments.filter(
        payment_date__gte=start_date,
        payment_date__lte=end_date
    ).order_by('-payment_date')
    
    last_payment = payments.first()
    total_amount = payments.aggregate(total=Sum('amount'))['total'] or Decimal('0')
    
    # تحديد حالة العضو
    if not last_payment:
        status = 'متأخر'
        status_class = 'danger'
        days_since_payment = 0
    else:
        days_since_payment = (end_date - last_payment.payment_date).days
        if days_since_payment > 45:
            status = 'متأخر'
            status_class = 'danger'
        elif days_since_payment > 30:
            status = 'تحذير'
            status_class = 'warning'
        else:
            status = 'منتظم'
            status_class = 'success'

    return {
        'status': status,
        'status_class': status_class,
        'days_since_payment': days_since_payment,
        'last_payment': last_payment,
        'total_amount': total_amount,
        'payments_count': payments.count()
    }


def create_new_cycle_round(organization):
    """إنشاء دورة جديدة للجمعية الدورية"""
    from .models import CycleRound, CycleMember

    if not organization.is_cyclic_organization():
        return None

    if not organization.can_start_new_cycle():
        return None

    # إنشاء الدورة الجديدة
    round_number = organization.get_next_cycle_number()
    start_date = organization.cycle_start_date or timezone.now().date()
    end_date = start_date + timedelta(days=organization.cycle_duration_days)

    cycle_round = CycleRound.objects.create(
        organization=organization,
        round_number=round_number,
        start_date=start_date,
        end_date=end_date,
        status='active'
    )

    # إضافة جميع الأعضاء النشطين للدورة
    active_members = organization.members.filter(is_active=True)
    for member in active_members:
        # حساب عدد مرات الاستلام في دورات سابقة
        received_count = member.get_received_count()
        has_received = received_count > 0

        CycleMember.objects.create(
            cycle_round=cycle_round,
            member=member,
            has_received_before=has_received,
            received_count=received_count
        )

    # تحديث حالة الجمعية
    organization.current_cycle = round_number
    organization.is_cycle_active = True
    organization.save()

    return cycle_round


def perform_random_draw(cycle_round):
    """إجراء السحب العشوائي لاختيار الفائز"""
    from .models import CycleMember

    # الحصول على الأعضاء المؤهلين للسحب (الذين دفعوا ومؤهلين)
    eligible_members = cycle_round.cycle_members.filter(
        payment_status='paid',
        is_eligible_for_draw=True
    )

    if not eligible_members.exists():
        return None

    # ترتيب الأعضاء حسب عدد مرات الاستلام (الأقل استلاماً أولاً)
    eligible_members = eligible_members.order_by('received_count')

    # الحصول على أقل عدد استلام
    min_received_count = eligible_members.first().received_count

    # اختيار الأعضاء الذين لديهم أقل عدد استلام
    lowest_received_members = eligible_members.filter(received_count=min_received_count)

    # السحب العشوائي من بين الأعضاء الذين لديهم أقل عدد استلام
    winner_cycle_member = random.choice(list(lowest_received_members))
    winner_member = winner_cycle_member.member

    # تحديث الدورة
    cycle_round.winner_member = winner_member
    cycle_round.draw_date = timezone.now()
    cycle_round.status = 'completed'

    # حساب إجمالي المبلغ
    total_amount = cycle_round.cycle_members.filter(
        payment_status='paid'
    ).aggregate(total=Sum('paid_amount'))['total'] or Decimal('0')

    cycle_round.total_amount = total_amount
    cycle_round.save()

    # تحديث حالة العضو الفائز
    winner_cycle_member.has_received_before = True
    # تحديث عدد مرات الاستلام (العدد الحالي + 1)
    winner_cycle_member.received_count = winner_cycle_member.received_count + 1
    winner_cycle_member.save()

    return winner_member


def get_cycle_statistics(organization):
    """الحصول على إحصائيات الجمعية الدورية"""
    if not organization.is_cyclic_organization():
        return None

    current_round = organization.get_current_cycle_round()
    total_rounds = organization.cycle_rounds.count()
    completed_rounds = organization.cycle_rounds.filter(status='completed').count()

    stats = {
        'total_rounds': total_rounds,
        'completed_rounds': completed_rounds,
        'current_round': current_round,
        'total_members': organization.get_total_members(),
        'monthly_amount': organization.monthly_amount,
    }

    if current_round:
        paid_members = current_round.cycle_members.filter(payment_status='paid').count()
        total_collected = current_round.cycle_members.filter(
            payment_status='paid'
        ).aggregate(total=Sum('paid_amount'))['total'] or Decimal('0')

        stats.update({
            'current_round_number': current_round.round_number,
            'paid_members_count': paid_members,
            'unpaid_members_count': organization.get_total_members() - paid_members,
            'total_collected_current': total_collected,
            'can_draw': paid_members > 0 and not current_round.winner_member,
        })

    return stats


def update_cycle_member_payment(cycle_member, amount, payment_date=None):
    """تحديث دفعة عضو الدورة"""
    if not payment_date:
        payment_date = timezone.now().date()

    cycle_member.paid_amount = amount
    cycle_member.payment_date = payment_date

    # تحديد حالة الدفع
    expected_amount = cycle_member.cycle_round.organization.monthly_amount
    if amount >= expected_amount:
        cycle_member.payment_status = 'paid'
        cycle_member.is_eligible_for_draw = True
    elif amount > 0:
        cycle_member.payment_status = 'partial'
        cycle_member.is_eligible_for_draw = False
    else:
        cycle_member.payment_status = 'not_paid'
        cycle_member.is_eligible_for_draw = False

    cycle_member.save()
    return cycle_member
