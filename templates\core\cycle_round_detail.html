{% extends 'base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}تفاصيل الدورة رقم {{ cycle_round.round_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>
                        <i class="fas fa-sync-alt me-2 text-primary"></i>
                        الدورة رقم {{ cycle_round.round_number }}
                    </h2>
                    <p class="text-muted mb-0">{{ organization.name }}</p>
                </div>
                <div>
                    <a href="{% url 'cyclic_dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        رجوع
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الدورة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h6>الأعضاء الذين دفعوا</h6>
                    <h3>{{ paid_count }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h6>الأعضاء المتأخرين</h6>
                    <h3>{{ unpaid_count }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h6>إجمالي المبلغ المجموع</h6>
                    <h3>{{ total_collected|format_currency }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h6>حالة الدورة</h6>
                    <h5>
                        {% if cycle_round.status == 'active' %}
                        <span class="badge bg-warning">نشطة</span>
                        {% elif cycle_round.status == 'completed' %}
                        <span class="badge bg-success">مكتملة</span>
                        {% else %}
                        <span class="badge bg-secondary">{{ cycle_round.get_status_display }}</span>
                        {% endif %}
                    </h5>
                </div>
            </div>
        </div>
    </div>

    <!-- السحب العشوائي -->
    {% if can_draw %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-dice me-2"></i>
                        السحب العشوائي
                    </h5>
                </div>
                <div class="card-body">
                    <p>يمكن الآن إجراء السحب العشوائي لاختيار العضو الذي سيستلم المبلغ هذه الدورة.</p>
                    <button type="button" class="btn btn-success btn-lg" onclick="performDraw()">
                        <i class="fas fa-dice me-2"></i>
                        إجراء السحب العشوائي
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% elif cycle_round.winner_member %}
    <!-- نتيجة السحب -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        نتيجة السحب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="text-success">
                                <i class="fas fa-user-check me-2"></i>
                                الفائز: {{ cycle_round.winner_member.name }}
                            </h4>
                            <p class="mb-0">
                                <i class="fas fa-phone me-2"></i>
                                {{ cycle_round.winner_member.phone }}
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المبلغ المستحق: {{ cycle_round.total_amount|format_currency }}
                            </p>
                            {% if cycle_round.draw_date %}
                            <p class="mb-0 text-muted">
                                <i class="fas fa-calendar me-2"></i>
                                تاريخ السحب: {{ cycle_round.draw_date|date:"Y/m/d H:i" }}
                            </p>
                            {% endif %}
                        </div>
                        <div class="col-md-4 text-center">
                            {% if not cycle_round.is_amount_received %}
                            <button type="button" class="btn btn-warning" onclick="markAsReceived()">
                                <i class="fas fa-hand-holding-usd me-2"></i>
                                تأكيد الاستلام
                            </button>
                            {% else %}
                            <span class="badge bg-success fs-6">
                                <i class="fas fa-check me-2"></i>
                                تم الاستلام
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- قائمة الأعضاء -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        أعضاء الدورة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>حالة الدفع</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>تاريخ الدفع</th>
                                    <th>مؤهل للسحب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for cycle_member in cycle_members %}
                                <tr id="member-{{ cycle_member.member.id }}">
                                    <td>
                                        <strong>{{ cycle_member.member.name }}</strong>
                                        {% if cycle_member.received_count > 0 %}
                                        <br><small class="text-muted">
                                            <i class="fas fa-check-circle text-success"></i>
                                            استلم {{ cycle_member.received_count }} مرة
                                        </small>
                                        {% endif %}
                                    </td>
                                    <td>{{ cycle_member.member.phone }}</td>
                                    <td>
                                        {% if cycle_member.payment_status == 'paid' %}
                                        <span class="badge bg-success">دفع</span>
                                        {% elif cycle_member.payment_status == 'partial' %}
                                        <span class="badge bg-warning">دفع جزئي</span>
                                        {% else %}
                                        <span class="badge bg-danger">لم يدفع</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ cycle_member.paid_amount|format_currency }}</td>
                                    <td>
                                        {% if cycle_member.payment_date %}
                                        {{ cycle_member.payment_date|date:"Y/m/d" }}
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if cycle_member.is_eligible_for_draw %}
                                        <i class="fas fa-check-circle text-success"></i>
                                        {% else %}
                                        <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="updatePayment({{ cycle_member.member.id }})">
                                            <i class="fas fa-edit"></i>
                                            تحديث الدفعة
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تحديث الدفعة -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث دفعة العضو</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="paymentForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="amount" class="form-label">المبلغ المدفوع</label>
                        <input type="number" class="form-control" id="amount" name="amount" 
                               step="0.01" min="0" value="{{ organization.monthly_amount }}">
                    </div>
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">تاريخ الدفع</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="savePayment()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentMemberId = null;

function updatePayment(memberId) {
    currentMemberId = memberId;
    // تعيين التاريخ الحالي
    document.getElementById('payment_date').value = new Date().toISOString().split('T')[0];
    new bootstrap.Modal(document.getElementById('paymentModal')).show();
}

function savePayment() {
    const form = document.getElementById('paymentForm');
    const formData = new FormData(form);
    
    fetch(`/cyclic/{{ organization.id }}/rounds/{{ cycle_round.id }}/members/${currentMemberId}/payment/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    });
}

function performDraw() {
    if (!confirm('هل أنت متأكد من إجراء السحب العشوائي؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }
    
    fetch(`/cyclic/{{ organization.id }}/rounds/{{ cycle_round.id }}/draw/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`تم السحب بنجاح!\nالفائز: ${data.winner_name}\nالهاتف: ${data.winner_phone}\nالمبلغ: ${data.total_amount} شيقل`);
            location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    });
}
</script>
{% endblock %}
