# Generated manually for adding email field to Member model

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_remove_loans'),
    ]

    operations = [
        migrations.AddField(
            model_name='member',
            name='email',
            field=models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني'),
        ),
    ]
